{"__section_common": "==================== 通用 ====================", "common": {"cancel": "取消", "confirm": "确认", "close": "关闭"}, "__section_nav": "==================== 顶部导航和公共元素 ====================", "announcement": "公告", "chinaTime": "中国时间", "home": "首页", "hotProducts": "热卖商品", "transport": "转运", "guide": "新手指南", "helpCenter": "帮助中心", "bulkPurchase": "批量采购", "notes": "公告", "loginRegister": "登录/注册", "login": "登录", "register": "注册", "cart": "购物车", "homeTitle": "中国商品代购专业平台", "__section_steps": "==================== 流程步骤 ====================", "step1": "下单并付款", "step2": "平台采购", "step3": "提交发货", "step4": "收货", "__section_home": "==================== 首页模块标题 ====================", "module1Title": "精选推荐", "module2Title": "大家都在买", "module3Title": "好物专题", "more": "更多", "searchPlaceholder": "搜索商品或链接", "ourAdvantages": "我们的优势", "featuredTopics": "精选专题", "__section_user": "==================== 用户相关 ====================", "mobileApp": "手机APP", "wechatOfficial": "微信公众号", "pack": "新人礼包", "memberCenter": "会员中心", "message": "消息", "favorite": "收藏", "top": "顶部", "__section_features": "==================== 特色功能 ====================", "features": ["一站式服务", "更专业", "更高效", "更快捷"], "__section_sizeGuide": "==================== 尺码助手 ====================", "sizeGuide": {"title": "尺码助手", "description": "选择合适的尺码，让购物更轻松", "toolTitle": "尺码对照工具", "toolDescription": "选择单位、国家和服装类型，查看详细的尺码对照表", "unit": "单位", "country": "国家/地区", "category": "服装类型", "notice": "请注意，这只是一个粗略的换算表，因为不同品牌和商品之间的尺码可能会有所不同。建议在购买前查看产品的详细尺码表并根据您的实际测量值选择合适的尺码。", "units": {"cm": "厘米", "in": "英寸"}, "categories": {"womenTop": "女士上衣", "menTop": "男士上衣", "womenPants": "女士裤子", "menPants": "男士裤子", "womenShoes": "女士鞋子", "menShoes": "男士鞋子", "ring": "戒指"}, "countries": {"US": "美国", "UK": "英国", "EU": "欧盟", "JP": "日本", "AU": "澳大利亚", "RU": "俄罗斯", "BR": "巴西", "IN": "印度", "CA": "加拿大", "KR": "韩国", "NZ": "新西兰", "DE": "德国", "FR": "法国", "SW": "瑞典"}, "howToUse": {"title": "如何使用尺码助手", "step1": {"title": "选择单位", "description": "选择您习惯使用的测量单位：厘米(cm)或英寸(in)"}, "step2": {"title": "选择国家/地区", "description": "选择您想要参考的尺码标准，不同国家的尺码标准可能不同"}, "step3": {"title": "选择服装类型", "description": "根据您要购买的商品类型选择相应的服装分类"}, "step4": {"title": "查看尺码表", "description": "参考表格中的数据，根据您的身体测量值选择合适的尺码"}}, "measureGuide": {"title": "测量指南", "tip1": {"title": "使用软尺测量", "description": "使用柔软的卷尺进行测量，确保贴合身体但不要过紧"}, "tip2": {"title": "穿合适的内衣", "description": "测量时穿着平时常穿的内衣，以获得最准确的测量结果"}, "tip3": {"title": "选择合适的时间", "description": "建议在一天中的同一时间测量，避免因身体状态变化影响结果"}}, "important": {"title": "重要提示", "note1": "不同品牌的尺码可能存在差异，建议优先参考商品详情页的尺码表", "note2": "如果您的测量值介于两个尺码之间，建议选择较大的尺码", "note3": "对于弹性面料的服装，可以选择相对合身的尺码", "note4": "如有疑问，建议联系客服获取专业的尺码建议"}}, "__section_footer": "==================== 页脚导航 ====================", "footer": {"title1": "购物指南", "links1": ["新手上路", "商品下单", "转运下单"], "title2": "支付说明", "links2": ["信用卡支付", "借记卡支付", "Paypal支付", "微信支付", "支付宝支付"], "title3": "配送说明", "links3": ["运输方式", "海关税项", "禁运物品", "其他注意事项"], "title4": "售后服务", "links4": ["商品存储政策", "退换货政策"], "title5": "关于我们", "links5": ["联系我们", "加入我们", "商务合作"]}, "__section_images": "==================== 图片资源 ====================", "topAdImage": "/src/assets/upload/topAd.jpeg", "logoImage": "/src/assets/images/logo.png", "searchBgImage": "/src/assets/images/searchBgImage.png", "__section_labels": "==================== 表单标签 ====================", "label": {"email": "邮箱", "password": "密码", "rememberMe": "记住我", "forgotPassword": "忘记密码?", "loginWithThird": "使用第三方账号登录", "noAccount": "没有账号?", "registerNow": "立即注册", "username": "用户名", "agree": "我已阅读并同意", "userAgreement": "用户协议", "and": "和", "privatePolicy": "隐私政策", "haveAccount": "已有账号?", "loginNow": "去登录", "verifyCode": "验证码", "newPassword": "新密码"}, "__section_placeholders": "==================== 表单占位符 ====================", "placeholder": {"email": "请输入邮箱", "password": "请输入密码", "username": "请输入用户名", "verifyCode": "请输入验证码", "newPassword": "请输入新密码"}, "__section_titles": "==================== 页面标题 ====================", "title": {"login": "登录", "register": "注册", "resetPassword": "重置密码", "userAgreement": "用户协议", "privacyPolicy": "隐私政策", "cookiePolicy": "<PERSON><PERSON>政策"}, "__section_buttons": "==================== 按钮文本 ====================", "button": {"login": "登录", "register": "注册", "myCenter": "个人中心", "myOrders": "我的订单", "myMessage": "我的消息", "logout": "退出", "sendCode": "发送验证码", "resend": "重新发送", "resetPassword": "重置密码"}, "__section_validations": "==================== 表单验证 ====================", "validations": {"required": "此字段是必填项。", "email": "无效的电子邮件格式。", "minLength": "此字段至少需要 {min} 个字符。", "maxLength": "此字段最多允许 {max} 个字符。"}, "__section_notify": "==================== 通知消息 ====================", "notify": {"registerSuccess": "注册成功!", "loginSuccess": "登录成功!", "verifyCodeSent": "验证码已发送！", "addedToWishlist": "已添加到收藏夹", "addToWishlistFailed": "添加到收藏夹失败", "addedToCart": "已添加到购物车", "addToCartFailed": "添加到购物车失败"}, "__section_menu": "==================== 菜单导航 ====================", "menu": {"home": "主页", "account": "我的账户", "assets": "账户资产", "balance": "余额", "coupons": "优惠券", "points": "积分", "orders": "我的订单", "orderProduct": "购物订单", "orderTransfer": "转运订单", "warehouse": "我的仓库", "parcel": "我的包裹", "messages": "消息中心", "msgInbox": "站内信", "msgConsult": "我的工单", "wishlist": "我的收藏", "settings": "个人设置", "profile": "个人信息", "addresses": "收货地址", "security": "账户安全", "dashboard": "个人中心"}, "__section_account_dashboard": "==================== 个人中心首页 ====================", "accountDashboard": {"userAvatar": "用户头像", "respectedUser": "尊敬的用户", "editProfile": "修改信息", "securitySettings": "安全设置", "accountBalance": "账户余额 (元)", "recharge": "充值", "myPoints": "我的积分", "view": "查看", "availableCoupons": "可用优惠券", "pendingTasks": "待处理事项", "noPendingTasks": "暂无待处理事项", "myMessages": "我的消息", "markAllAsRead": "全部标为已读", "clearAllMessages": "清空消息", "noMessages": "暂无消息", "viewAll": "查看全部", "quickAccess": "快捷入口", "recentOrders": "最近订单", "noOrders": "暂无订单", "goShopping": "去购物", "orderNumber": "订单号: ", "viewAllOrders": "查看全部订单", "shortcuts": {"myOrders": "我的订单", "myTransfers": "我的转运单", "shippingAddresses": "收货地址", "myWishlist": "我的收藏", "accountBalance": "账户余额", "coupons": "优惠券", "customerService": "在线客服"}, "errors": {"getUserInfoFailed": "获取用户信息失败", "getWalletFailed": "获取钱包信息失败", "getNumDataFailed": "获取数量数据失败", "getRecentOrdersFailed": "获取最近订单失败", "getPendingTransfersFailed": "获取待处理转运单失败", "getMessagesFailed": "获取消息失败"}, "weekdays": {"sunday": "周日", "monday": "周一", "tuesday": "周二", "wednesday": "周三", "thursday": "周四", "friday": "周五", "saturday": "周六"}, "orderStatus": {"pending": "待创建", "unpaid": "待支付", "unshipped": "待发货", "shipped": "已发货", "completed": "已完成", "closed": "已关闭", "unknown": "未知状态"}, "pendingTasksDesc": {"unpaidOrders": "个订单待支付", "unpaidOrdersDesc": "请尽快完成支付，以免订单自动取消", "shippedOrders": "个订单待收货", "shippedOrdersDesc": "您的包裹已发出，请注意查收", "uncommentedOrders": "个订单待评价", "uncommentedOrdersDesc": "评价订单可获得积分奖励", "pendingTransfers": "个转运单待处理", "pendingTransfersDesc": "您有转运单需要处理"}}, "__section_account_balance": "==================== 余额页面 ====================", "accountBalance": {"title": "我的余额", "currentBalance": "当前余额", "totalIncome": "总收入", "totalExpense": "总支出", "recharge": "充值", "tabs": {"all": "全部", "income": "收入", "expense": "支出"}, "loading": "加载中...", "noTransactions": "暂无交易记录", "pagination": "共 {total} 条记录，第 {current} / {totalPages} 页", "table": {"transactionType": "交易类型", "amount": "金额", "transactionTime": "交易时间"}, "transactionTypes": {"recharge": "充值", "withdraw": "提现", "payment": "支付", "refund": "退款", "other": "其他"}}, "__section_account_coupons": "==================== 优惠券页面 ====================", "accountCoupons": {"title": "我的优惠券", "tabs": {"available": "未使用", "used": "已使用", "expired": "已过期", "default": "优惠券"}, "getMore": "领取更多", "getCoupons": "去领取优惠券", "loading": "加载中...", "noCoupons": "暂无{status}优惠券", "pagination": "共 {total} 条记录，第 {current} / {totalPages} 页", "errors": {"fetchFailed": "获取优惠券列表失败", "fetchError": "获取优惠券列表出错"}}, "__section_account_points": "==================== 积分页面 ====================", "accountPoints": {"title": "我的积分", "currentPoints": "当前积分", "pointsRules": "积分规则", "tabs": {"all": "全部", "income": "获取", "expense": "消费"}, "loading": "加载中...", "noRecords": "暂无积分记录", "pagination": "共 {total} 条记录，第 {current} / {totalPages} 页", "table": {"type": "积分类型", "change": "积分变动", "time": "时间", "description": "说明"}}, "__section_account_orders": "==================== 订单页面 ====================", "accountOrders": {"title": "我的订单", "tabs": {"unpaid": "待付款", "purchasing": "采购中", "warehoused": "已入库", "additionalPayment": "待补款", "all": "全部"}, "search": {"placeholder": "请输入订单号"}, "table": {"productDetails": "商品详情", "unitPrice": "单价", "quantity": "数量", "amount": "金额", "orderStatus": "订单状态", "actions": "操作"}, "orderInfo": {"orderNumber": "订单编号：", "totalAmount": "总金额：", "orderDetails": "订单详情", "cancelOrder": "取消订单", "payNow": "立即支付", "totalItems": "共{count}件商品 合计："}, "noOrders": "暂无订单记录", "pagination": "共 {total} 条记录，第 {current} / {totalPages} 页", "errors": {"emptyOrderId": "订单ID不能为空", "cancelFailed": "取消订单失败", "cancelError": "取消订单出错"}, "confirmations": {"cancelTitle": "取消订单", "cancelMessage": "确定要取消该订单吗？", "cancelSuccess": "订单已取消"}, "status": {"unpaid": "待付款", "purchasing": "采购中", "shipping": "待收货", "warehoused": "已入库", "cancelled": "已取消", "additionalPayment": "待补款", "completed": "已完成", "unknown": "未知状态"}}, "__section_help": "==================== 帮助中心页面 ====================", "help": {"title": "帮助中心", "searchPlaceholder": "搜索帮助内容", "loading": "加载中...", "sidebar": {"homePage": "帮助中心首页"}, "contactSupport": {"title": "没有找到您需要的帮助？", "button": "联系客服"}, "__section_customer_service": "==================== 客服相关 ====================", "customerService": {"title": "在线客服", "description": "有问题？联系我们的客服团队", "onlineStatus": "在线", "offlineStatus": "离线", "workingHours": "工作时间: 9:00-18:00 (周一至周五)", "pleaseLogin": "请先登录后再咨询客服", "inputPlaceholder": "请输入你要咨询的问题", "send": "发送", "connecting": "连接中...", "connectionSuccess": "连接成功", "connectionFailed": "连接失败，请稍后再试", "newMessage": "有新消息", "loadMore": "加载更多", "noMoreMessages": "没有更多消息了", "tools": {"emoji": "表情", "image": "图片", "product": "商品", "order": "订单"}}, "chat": {"loginRequired": "请先登录后再使用客服功能", "goToLogin": "去登录", "humanConnected": "客服连接成功", "inputPlaceholder": "请输入你要咨询的问题", "send": "发送", "onlyEmojisAllowed": "为了安全起见，只允许发送文字和表情", "backToHome": "返回主页", "humanService": "人工客服", "aiService": "智能客服", "reconnecting": "会话重连中", "newMessage": "收到新消息", "messageRead": "客服已读您的消息", "sendMessageError": "发送消息失败", "audioPlayError": "播放提示音失败", "websocketReconnectError": "WebSocket重连功能不可用", "status": {"connecting": "客服正在接入...", "connected": "客服连接成功", "reconnecting": "正在重新连接...", "disconnected": "连接已断开"}, "robot": {"welcome": "您好，我是智能客服助手", "intro": "请选择您想了解的问题，或直接点击下方按钮联系人工客服", "categories": "常见问题分类", "popularQuestions": "热门问题", "loading": "加载中...", "helpfulQuestion": "这个回答是否解决了您的问题？", "solved": "已解决", "notSolved": "未解决", "contactHuman": "联系人工客服", "fetchError": "获取帮助中心数据失败", "loadError": "获取FAQ详情失败", "thanksFeedback": "感谢您的反馈！", "connectingHuman": "正在连接人工客服...", "avatar": "机器人", "fetchDataError": "获取帮助中心数据失败", "fetchDetailError": "获取FAQ详情失败", "processHtmlError": "处理HTML内容时出错"}, "sendFailed": "消息发送失败，请检查网络连接", "connectionError": "连接出错，请稍后再试", "messageLimit": "发送消息过于频繁，请稍后再试", "loadMore": "加载更多", "newMessageTip": "有新消息", "getMessageListFailed": "获取消息列表失败", "loadMoreFailed": "加载更多消息失败", "noMoreMessages": "没有更多消息了", "receiveMessageFailed": "接收消息失败"}, "faq": {"title": "常见问题解答 (FAQ)", "description": "以下是用户最常咨询的问题，点击问题可直接查看详细解答", "viewMore": "更多常见问题", "pageTitle": "常见问题解答", "pageDescription": "我们收集了用户最常见的问题和解答，希望能帮助您快速解决疑问", "noData": "暂无常见问题"}, "categories": {"title": "帮助分类", "viewMore": "查看更多"}, "videoTutorials": {"title": "视频教程"}, "moreFAQ": {"title": "更多常见问题"}, "contactDialog": {"title": "联系客服"}, "buttons": {"close": "关闭"}, "pagination": {"info": "共 {total} 条记录，第 {current} / {pages} 页"}, "search": {"title": "搜索结果", "loading": "正在搜索...", "resultsCount": "找到 {count} 条相关结果", "noResults": "未找到与\"{query}\"相关的内容", "initialText": "请输入关键词进行搜索", "searchButton": "搜索", "browseAllHelp": "浏览所有帮助内容", "suggestions": {"title": "搜索建议：", "checkSpelling": "检查您的拼写", "trySimpler": "尝试使用更简单的关键词", "tryRelated": "尝试使用相关的关键词", "browseCategories": "浏览帮助分类查找相关内容"}}, "detail": {"updatedAt": "更新于", "views": "次阅读", "feedback": {"title": "这篇文章对您有帮助吗？", "helpful": "有帮助", "notHelpful": "没帮助", "helpfulCount": "{count}人认为有帮助", "notHelpfulCount": "{count}人认为没帮助"}, "relatedArticles": "相关文章", "loading": "加载中...", "notFound": "未找到相关帮助内容", "backToHelp": "返回帮助中心"}, "categoryNotFound": "未找到该分类", "backToHelp": "返回帮助中心"}, "__section_account_security": "==================== 账户安全页面 ====================", "security": {"title": "账户安全", "buttons": {"modify": "修改", "set": "设置", "bind": "绑定", "unbind": "解绑", "verify": "验证", "cancel": "取消", "confirm": "确认"}, "cards": {"passwordManagement": {"title": "密码管理", "loginPassword": {"label": "登录密码", "description": "用于保护账户信息安全"}, "paymentPassword": {"label": "支付密码", "description": "用于账户余额支付和提现等操作"}, "autoPayment": {"label": "补款授权", "description": "开启后如遇到需要补款的情况可以自动使用钱包余额支付"}}, "accountBinding": {"title": "账号绑定", "email": {"label": "邮箱", "verified": "已验证", "unverified": "未验证"}, "mobile": {"label": "手机", "notBound": "未绑定"}, "wechat": {"label": "微信"}, "google": {"label": "Google"}, "facebook": {"label": "Facebook"}, "status": {"bound": "已绑定", "notBound": "未绑定"}}}, "dialogs": {"loginPassword": {"title": "修改登录密码", "fields": {"currentPassword": "当前密码", "newPassword": "新密码", "confirmPassword": "确认新密码"}, "validations": {"currentPasswordRequired": "请输入当前密码", "newPasswordRequired": "请输入新密码", "passwordLength": "密码长度至少为6位", "passwordDifferent": "新密码不能与当前密码相同", "confirmPasswordRequired": "请确认新密码", "passwordMatch": "两次输入的密码不一致"}}, "paymentPassword": {"titleModify": "修改支付密码", "titleSet": "设置支付密码", "titleReset": "重置支付密码", "fields": {"currentPassword": "当前支付密码", "newPassword": "新支付密码", "confirmPassword": "确认新支付密码", "email": "邮箱地址", "emailCode": "邮件验证码"}, "validations": {"currentPasswordRequired": "请输入当前支付密码", "newPasswordRequired": "请输入新支付密码", "confirmPasswordRequired": "请确认新支付密码", "passwordLength": "支付密码必须为6位数字", "passwordDifferent": "新支付密码不能与当前支付密码相同", "passwordMatch": "两次输入的支付密码不一致", "emailRequired": "请输入邮箱地址", "emailInvalid": "请输入正确的邮箱地址", "emailCodeRequired": "请输入验证码"}, "buttons": {"forgotPassword": "忘记支付密码？", "getEmailCode": "获取验证码", "resetPassword": "确认重置"}, "messages": {"setSuccess": "支付密码设置成功", "updateSuccess": "支付密码修改成功", "resetSuccess": "支付密码重置成功", "emailCodeSent": "验证码已发送到您的邮箱", "resetTip": "请输入您的邮箱地址，我们将发送验证码用于重置支付密码", "setTip": "首次设置支付密码需要邮件验证码验证，验证码将发送到您的邮箱", "passwordTip": "支付密码用于账户余额支付和提现等操作，请设置6位数字密码", "emailRequired": "请先绑定邮箱地址"}}}}, "__section_bulk_purchase": "==================== 批量采购页面 ====================", "bulkPage": {"banner": {"title": "企业批量采购解决方案", "subtitle": "为您的企业提供一站式海外商品批量采购服务，省时省力更省钱", "button": "立即咨询"}, "advantages": {"title": "我们的优势", "items": {"price": {"title": "价格优势", "desc": "直接对接海外供应商，省去中间环节，为您节省采购成本"}, "quality": {"title": "品质保障", "desc": "严格的质量把控流程，确保每一件商品都符合您的要求"}, "variety": {"title": "品类齐全", "desc": "覆盖电子、服装、美妆、母婴等多个品类，满足多样化需求"}, "service": {"title": "专业服务", "desc": "一对一专属客服，全程跟进采购流程，解决您的后顾之忧"}}}, "scenarios": {"title": "适用场景", "items": {"gifts": {"title": "企业礼品采购", "desc": "为企业活动、节日庆典、员工福利等场合提供高品质的礼品采购方案，彰显企业品味与诚意。"}, "retail": {"title": "零售商货源供应", "desc": "为电商卖家、实体店铺提供稳定的海外商品货源，助力您的业务拓展与利润增长。"}, "events": {"title": "活动物资采购", "desc": "为展会、促销活动、团建等各类活动提供一站式物资采购服务，让您的活动更加出彩。"}}}, "process": {"title": "服务流程", "steps": {"communication": {"title": "需求沟通", "desc": "提交您的采购需求，我们的专业顾问将与您详细沟通，了解具体要求"}, "planning": {"title": "方案制定", "desc": "根据您的需求，我们将为您量身定制采购方案，包括商品选择、价格预算等"}, "confirmation": {"title": "确认合作", "desc": "确认采购方案后，签订合作协议，支付预付款，正式启动采购流程"}, "procurement": {"title": "商品采购", "desc": "我们的专业团队将按照方案进行商品采购，严格把控商品质量"}, "logistics": {"title": "物流配送", "desc": "商品采购完成后，我们将安排国际物流，确保商品安全、及时送达"}, "completion": {"title": "验收完成", "desc": "您收到商品后进行验收，确认无误后支付尾款，完成整个采购流程"}}}, "faq": {"title": "常见问题", "questions": {"minQuantity": {"question": "批量采购的最低数量要求是多少？", "answer": "我们的批量采购服务没有严格的最低数量限制，但通常建议单品采购数量在10件以上，或者总采购金额在5000元以上，这样能够更好地发挥批量采购的价格优势。"}, "payment": {"question": "批量采购的付款方式有哪些？", "answer": "我们支持多种付款方式，包括银行转账、支付宝、微信支付等。通常采用\"预付款+尾款\"的付款模式，具体比例可根据采购金额和合作情况协商确定。"}, "delivery": {"question": "批量采购的交货周期是多久？", "answer": "交货周期取决于采购商品的类型、数量以及供应商的备货情况。一般情况下，从确认订单到商品送达，整个流程需要15-30天。如有特殊需求，可与我们的客服人员沟通，我们会尽量满足您的时间要求。"}, "quality": {"question": "如何保证采购商品的质量？", "answer": "我们有严格的质量控制流程，包括供应商筛选、商品检验、出货前质检等多个环节。同时，我们也提供商品质量保障服务，如发现质量问题，可按照协议约定进行退换货处理。"}, "sample": {"question": "是否可以提供样品？", "answer": "是的，对于大额采购订单，我们可以提供样品服务，让您在下大单前先确认商品品质。样品费用和运费通常需要客户承担，但在后续大单确认后，我们可以在总金额中抵扣样品费用。"}}}, "contact": {"title": "联系我们", "subtitle": "如果您对批量采购服务有任何疑问或需求，请通过以下方式联系我们", "customerService": {"title": "客服咨询", "phone": {"label": "电话咨询", "value": "************（工作日 9:00-18:00）"}, "email": {"label": "邮件咨询", "value": "<EMAIL>"}, "online": {"label": "在线客服", "value": "点击右下角客服图标，立即咨询"}}, "inquiry": {"title": "需求提交", "form": {"name": {"label": "您的姓名 *", "placeholder": "请输入您的姓名", "required": "请输入姓名", "minLength": "姓名至少需要2个字符", "maxLength": "姓名不能超过30个字符"}, "email": {"label": "电子邮箱 *", "placeholder": "我们将通过此邮箱与您联系", "required": "请输入电子邮箱", "invalid": "请输入有效的电子邮箱", "maxLength": "电子邮箱不能超过50个字符"}, "companyName": {"label": "公司名称", "placeholder": "选填项", "maxLength": "公司名称不能超过50个字符"}, "phone": {"label": "联系电话", "placeholder": "选填项，如：+86 18888888888", "invalid": "请输入有效的电话号码"}, "description": {"label": "采购需求描述 *", "placeholder": "请详细描述您的采购需求，包括商品类型、数量、预算等信息", "required": "请描述您的采购需求", "minLength": "描述至少需要10个字符", "maxLength": "描述不能超过500个字符"}, "submit": "提交需求"}, "success": {"title": "提交成功！", "message": "您的需求已提交成功，我们的客服人员将在24小时内与您联系", "button": "返回首页"}, "notification": {"success": "您的需求已提交成功，我们的客服人员将尽快与您联系", "error": "提交失败，请稍后重试"}}}}, "__section_account_addresses": "==================== 收货地址页面 ====================", "addresses": {"title": "收货地址", "loading": "加载中...", "noAddresses": "暂无收货地址，请点击\"新增地址\"按钮添加", "labels": {"default": "默认"}, "buttons": {"addAddress": "新增地址", "setDefault": "设为默认", "edit": "编辑", "delete": "删除", "cancel": "取消", "save": "保存地址"}, "dialog": {"addTitle": "新增收货地址", "editTitle": "编辑收货地址", "deleteConfirm": "确定要删除这个地址吗？"}, "pagination": {"info": "共 {total} 条记录，第 {current} / {pages} 页"}, "notifications": {"fetchFailed": "获取地址列表失败", "addSuccess": "地址添加成功", "updateSuccess": "地址更新成功", "saveError": "保存地址失败", "deleteSuccess": "地址删除成功", "deleteError": "删除地址失败", "setDefaultSuccess": "默认地址设置成功", "setDefaultError": "设置默认地址失败"}, "form": {"sections": {"addressInfo": "地址信息", "recipientInfo": "收件人信息", "regionInfo": "地区信息", "cityInfo": "城市信息", "detailAddress": "详细地址"}, "fields": {"alias": "地址别名", "aliasHint": "可选，如：家、公司等", "name": "收件人姓名", "mobile": "手机号码", "country": "国家/地区", "postCode": "邮政编码", "state": "省/州", "city": "城市", "detailAddress": "详细地址", "detailAddressHint": "请输入详细的街道、门牌号等信息", "defaultAddress": "设为默认收货地址"}, "validations": {"nameRequired": "请输入收件人姓名", "mobileRequired": "请输入手机号码", "countryRequired": "请选择国家/地区", "postCodeRequired": "请输入邮政编码", "postCodeInvalid": "请输入有效的邮政编码", "stateRequired": "请选择省/州", "stateInputRequired": "请输入省/州", "cityRequired": "请选择城市", "cityInputRequired": "请输入城市", "detailAddressRequired": "请输入详细地址"}, "placeholders": {"selectCountry": "请选择国家/地区"}}, "countries": {"china": "中国", "usa": "美国", "japan": "日本", "korea": "韩国", "uk": "英国", "germany": "德国", "france": "法国", "canada": "加拿大", "australia": "澳大利亚", "newZealand": "新西兰", "singapore": "新加坡", "malaysia": "马来西亚", "thailand": "泰国", "indonesia": "印度尼西亚", "philippines": "菲律宾", "vietnam": "越南", "russia": "俄罗斯", "india": "印度", "brazil": "巴西", "mexico": "墨西哥"}}, "__section_account_profile": "==================== 个人信息页面 ====================", "profile": {"title": "个人信息", "sections": {"basicInfo": "基本信息", "contactInfo": "联系方式", "security": "安全设置"}, "fields": {"username": "用户名", "nickname": "昵称", "gender": "性别", "avatar": "头像", "email": "邮箱", "mobile": "手机号码"}, "values": {"notSet": "未设置", "userAvatar": "用户头像", "avatarPreview": "头像预览", "verified": "已验证", "unverified": "未验证"}, "gender": {"secret": "保密", "male": "男", "female": "女"}, "countries": {"china": "中国", "usa": "美国", "japan": "日本", "korea": "韩国", "uk": "英国", "australia": "澳大利亚", "singapore": "新加坡"}, "buttons": {"edit": "编辑", "save": "保存", "cancel": "取消", "modify": "修改", "selectImage": "选择图片", "sendVerifyEmail": "发送验证邮件", "getCode": "获取验证码", "resendAfter": "{seconds}秒后重发"}, "placeholders": {"username": "请输入用户名", "nickname": "请输入昵称", "mobile": "请输入手机号码", "verificationCode": "请输入验证码"}, "validations": {"usernameRequired": "请输入用户名", "mobileRequired": "请输入手机号码", "codeRequired": "请输入验证码"}, "tips": {"avatarFormat": "支持 JPG、PNG 格式，文件大小不超过 2MB"}, "notifications": {"getUserInfoFailed": "获取用户信息失败，请刷新页面重试", "imageSizeLimit": "图片大小不能超过2MB", "basicInfoUpdated": "基本信息更新成功", "updateFailed": "更新失败，请重试", "saveFailed": "保存失败，请重试"}}, "__section_account_order_detail": "==================== 订单详情页面 ====================", "accountOrderDetail": {"title": "订单详情", "backToList": "返回订单列表", "loading": "加载订单信息中...", "notFound": "订单不存在或已被删除", "sections": {"basicInfo": "基本信息", "productInfo": "商品详情", "shippingInfo": "收货信息", "expressInfo": "快递信息"}, "fields": {"orderNumber": "订单编号：", "createTime": "创建时间：", "orderStatus": "订单状态：", "paymentMethod": "支付方式：", "paymentTime": "支付时间：", "paymentOrder": "支付订单：", "remarks": "备注信息：", "recipient": "收货人：", "contactPhone": "联系电话：", "shippingAddress": "收货地址：", "expressCompany": "快递公司：", "trackingNumber": "快递单号：", "expressRemarks": "快递备注："}, "table": {"productInfo": "商品信息", "unitPrice": "单价", "quantity": "数量", "subtotal": "小计"}, "summary": {"item": "商品信息", "price": "单价", "quantity": "数量", "subtotal": "小计", "unitPrice": "单价：", "quantityLabel": "数量：", "subtotalLabel": "小计："}, "price": {"totalProducts": "商品总额：", "shipping": "运费：", "discount": "优惠金额：", "actualPayment": "实付金额：", "serviceFee": "增值服务费：", "platformFee": "平台佣金："}, "buttons": {"cancelOrder": "取消订单", "payNow": "立即支付", "confirmReceipt": "确认收货", "reviewProduct": "评价商品", "deleteOrder": "删除订单", "trackShipment": "查询物流", "close": "关闭", "print": "打印订单"}, "placeholders": {"unpaid": "未支付", "none": "无"}, "logistics": {"title": "物流信息", "loading": "正在查询物流信息，请稍候...", "trackTitle": "物流轨迹", "noInfo": "暂无物流信息", "shipped": "已发货", "latestStatus": "最新状态"}, "confirmations": {"cancelTitle": "取消订单", "cancelMessage": "确定要取消该订单吗？", "cancelSuccess": "订单已取消", "cancelFailed": "取消订单失败", "receiptTitle": "确认收货", "receiptMessage": "确认已收到商品吗？", "receiptSuccess": "已确认收货", "receiptFailed": "确认收货失败", "deleteTitle": "删除订单", "deleteMessage": "确定要删除该订单吗？删除后将无法恢复！", "deleteSuccess": "订单已删除", "deleteFailed": "删除订单失败"}, "errors": {"fetchFailed": "获取订单详情失败", "fetchError": "获取订单详情出错", "trackFailed": "查询物流信息失败"}, "paymentMethods": {"wallet": "钱包余额", "wechat": "微信支付", "alipay": "支付宝", "unpaid": "未支付"}, "status": {"unpaid": "待付款", "purchasing": "采购中", "shipping": "待收货", "warehoused": "已入库", "cancelled": "已取消", "additionalPayment": "待补款", "completed": "已完成", "unknown": "未知状态"}, "progress": {"unpaid": "待付款", "purchasing": "平台采购", "warehoused": "已入库"}}, "__section_error": "==================== 错误信息 ====================", "error": {"requiredAgreement": "您必须勾选同意才能继续", "verifyCodeError": "验证码发送失败", "page": {"backToHome": "返回首页", "goBack": "返回上一页", "needHelp": "需要帮助？", "contactSupport": "如果问题持续存在，请联系我们的客服团队：", "contactSupportError": "如果您认为这是一个错误，请联系我们的客服团队：", "errorCode": "错误", "refreshPage": "刷新页面", "search": "搜索", "reLogin": "重新登录", "suggestions": "您可能想要访问：", "homePage": "首页", "productList": "商品列表", "userCenter": "个人中心", "helpCenter": "帮助中心", "titles": {"400": "错误请求", "401": "未授权", "403": "访问被拒绝", "404": "页面未找到", "500": "服务器错误", "503": "服务不可用", "default": "发生错误"}, "descriptions": {"400": "抱歉，您的请求包含错误的语法，无法被服务器理解。", "401": "抱歉，您需要登录后才能访问此页面。", "403": "抱歉，您没有权限访问此页面。", "404": "抱歉，您访问的页面不存在或已被移除。", "500": "抱歉，服务器出现了问题，我们正在努力修复中。", "503": "抱歉，服务暂时不可用，请稍后再试。", "default": "抱歉，发生了未知错误，请稍后再试。"}}}, "__section_products": "==================== 商品相关 ====================", "products": {"title": "商品列表", "categories": "商品分类", "allCategories": "全部商品", "showCategories": "显示分类", "sortBy": "排序方式", "sortOptions": {"newest": "最新上架", "priceAsc": "价格从低到高", "priceDesc": "价格从高到低", "popular": "人气优先"}, "noProducts": "暂无商品", "filter": "筛选", "gridView": "网格视图", "listView": "列表视图", "addToCart": "加入购物车", "addToWishlist": "加入收藏", "quickView": "快速查看", "outOfStock": "缺货", "inStock": "有货", "freeShipping": "免运费", "source": "来源", "shop": "店铺", "brand": "品牌", "price": "价格", "priceRange": "价格区间", "apply": "应用", "reset": "重置", "searchPlaceholder": "搜索商品", "totalItems": "共 {count} 件商品", "showingItems": "显示 {start}-{end} 件，共 {total} 件", "detail": {"productDetail": "商品详情", "productLink": "商品链接", "price": "价格", "save": "保存", "cancel": "取消", "priceEditTip": "修改价格提示", "stock": "库存", "freight": "运费", "quantity": "购买数量", "memo": "备注", "addToCart": "加入购物车", "buyNow": "立即购买", "addToWishlist": "收藏", "description": "商品描述", "purchaseNotes": "代购须知", "afterSale": "售后服务", "purchaseNotesContent": "代购须知：购买商品后，7日内发货，请耐心等待。", "afterSaleContent": "售后服务：支持7天无理由退货，退货需保证商品未使用且完好。", "selectSpec": "请选择规格", "insufficientStock": "库存不足", "addedToCart": "已加入购物车", "addToCartFailed": "添加到购物车失败，请稍后重试。", "addedToWishlist": "加入到心愿单"}}, "__section_verify": "==================== 邮箱验证 ====================", "verify": {"loading": "正在验证邮箱...", "success": {"title": "邮箱验证成功！", "greeting": "恭喜", "message": "您已成功在平台注册", "coupon": "欢迎优惠券已发送至您的账户：", "checkCoupon": "点击查看您的优惠券", "startShopping": "立即开始购物", "redirect": "页面将在", "seconds": "秒后自动跳转到首页"}, "failed": {"title": "验证失败", "message": "验证链接可能无效或已过期，请重试。", "invalidLink": "无效的验证链接。", "unexpectedError": "发生意外错误。", "help": "您可以尝试以下操作：", "checkEmail": "检查您是否点击了邮件中的正确链接", "tryAgain": "请求新的验证邮件", "contactSupport": "如果问题持续存在，请联系客服", "resend": "重新发送验证邮件", "goHome": "返回首页"}}, "__section_cookies_consent": "==================== Cookie同意提示 ====================", "cookiesConsent": {"consent": {"title": "Cookie使用提示", "message": "我们使用Cookie来改善您的浏览体验，个性化内容和广告，提供社交媒体功能并分析我们的流量。", "learnMore": "点击了解更多关于我们如何使用", "cookiesLink": "<PERSON><PERSON>", "andText": "和", "privacyLink": "隐私政策", "details": "的详细信息。", "customize": "自定义设置", "reject": "拒绝", "accept": "接受全部"}, "customize": {"title": "<PERSON><PERSON>设置", "essential": "必要<PERSON><PERSON>", "essentialDesc": "这些Cookie对网站的基本功能是必不可少的，无法禁用。", "performance": "性能和分析Cookie", "performanceDesc": "这些Cookie帮助我们了解用户如何与网站互动，以改进用户体验。", "functional": "功能性<PERSON><PERSON>", "functionalDesc": "这些Cookie使网站能够记住您的选择，提供增强的个性化功能。", "targeting": "定向和广告Cookie", "targetingDesc": "这些Cookie用于跟踪您在网站上的浏览习惯，以便向您展示相关广告。", "cancel": "取消", "save": "保存设置"}}, "__section_text": "==================== 文本内容 ====================", "text": {"lastUpdated": "最后更新日期", "loginPolicyText": "登录即表示您同意我们的", "rememberPassword": "记起密码了？", "resetPasswordDescription": "请输入您的邮箱地址，我们将发送验证码帮助您重置密码。"}, "__section_agreement": "==================== 用户协议 ====================", "agreement": {"introduction": {"title": "引言", "content": "欢迎使用LILISHOP服务。本用户协议(\"协议\")是您与LILISHOP之间就使用我们的服务所达成的法律协议。使用我们的服务，即表示您同意本协议的条款。请仔细阅读。"}, "services": {"title": "服务说明", "content": "LILISHOP是一个全球领先的代购电商服务平台，为用户提供跨境购物、转运、代购等服务。我们的服务可能会不断更新，服务的形式和性质可能会随时变化，恕不另行通知。"}, "account": {"title": "用户账户", "content1": "您需要创建账户才能使用我们的某些服务。您承诺提供准确、完整的注册信息，并在信息变更时及时更新。", "content2": "您有责任保护您的账户安全，包括保护您的密码和限制对您计算机的访问。您同意对您账户下发生的所有活动负责。"}, "userConduct": {"title": "用户行为", "content1": "您同意不会使用我们的服务进行任何违法或未经授权的活动，包括但不限于：", "item1": "违反任何适用的法律法规", "item2": "侵犯他人的知识产权或其他权利", "item3": "传播垃圾信息、欺诈信息或恶意软件", "item4": "干扰或破坏我们服务的正常运行"}, "purchases": {"title": "购买与支付", "content1": "通过我们的平台购买商品时，您同意支付所有适用的费用，包括商品价格、运费、税费和服务费。", "content2": "我们会尽力确保商品信息的准确性，但不对因信息错误导致的问题承担责任。最终的价格和条款以订单确认时的信息为准。"}, "shipping": {"title": "配送与转运", "content1": "我们提供国际转运服务，帮助您将商品从原产地运送到您指定的地址。", "content2": "配送时间仅为估计，可能受到多种因素影响，包括但不限于海关检查、天气条件和物流延误。", "content3": "您有责任确保提供的收货地址准确完整，并遵守目的地国家/地区的进口法规。"}, "returns": {"title": "退货与退款", "content1": "我们的退货政策受限于原始卖家的政策以及国际运输的特殊性。", "content2": "如需退货，您必须在收到商品后的规定时间内联系我们，并提供必要的证明文件。", "content3": "退款将按照原始支付方式处理，可能需要一定的处理时间。"}, "liability": {"title": "责任限制", "content1": "在法律允许的最大范围内，LILISHOP及其关联公司、员工、代理人不对因使用或无法使用我们的服务而导致的任何直接、间接、附带、特殊、惩罚性或后果性损害承担责任。", "content2": "我们的责任总额不会超过您在相关事件发生前12个月内为使用相关服务而支付给我们的金额。"}, "disputes": {"title": "争议解决", "content1": "本协议受中华人民共和国法律管辖。", "content2": "因本协议引起的或与之相关的任何争议，双方应首先通过友好协商解决。如协商不成，任何一方均可向有管辖权的法院提起诉讼。"}, "changes": {"title": "协议变更", "content1": "我们可能会不时修改本协议。修改后的协议将在我们的网站上发布，并在发布时生效。", "content2": "继续使用我们的服务即表示您接受修改后的协议。如果您不同意修改内容，请停止使用我们的服务。"}, "contact": {"title": "联系我们", "content": "如果您对本协议有任何疑问，请通过以下方式联系我们："}}, "__section_privacy": "==================== 隐私政策 ====================", "privacy": {"introduction": {"title": "引言", "content": "LILISHOP(\"我们\"、\"我们的\"或\"本公司\")尊重并保护用户的隐私。本隐私政策说明了我们如何收集、使用、披露、处理和保护您通过我们的网站、应用程序和服务提供给我们的信息。请仔细阅读本隐私政策，以了解我们的隐私实践。"}, "collection": {"title": "信息收集", "content1": "我们可能会收集以下类型的信息：", "item1": {"title": "个人识别信息", "content": "包括但不限于您的姓名、电子邮件地址、电话号码、邮寄地址、身份证号码（如适用于海关申报）。"}, "item2": {"title": "账户信息", "content": "您创建账户时提供的信息，如用户名、密码、安全问题及答案。"}, "item3": {"title": "交易信息", "content": "您的购买历史、支付方式、账单和配送信息。"}, "item4": {"title": "设备信息", "content": "设备标识符、IP地址、浏览器类型、操作系统版本、语言设置等。"}, "item5": {"title": "使用数据", "content": "关于您如何使用我们服务的信息，如浏览历史、搜索查询、点击行为等。"}, "item6": {"title": "<PERSON><PERSON>和类似技术", "content": "我们使用Cookie和类似技术收集信息，详见我们的Cookie政策。"}}, "use": {"title": "信息使用", "content1": "我们可能将收集的信息用于以下目的：", "item1": "提供、维护和改进我们的服务", "item2": "处理和完成您的交易", "item3": "管理您的账户，包括身份验证", "item4": "提供客户支持和响应您的请求", "item5": "发送服务通知和更新", "item6": "发送营销和促销信息（如您同意接收）", "item7": "防止欺诈和增强安全性", "item8": "进行数据分析和研究以改进我们的服务", "item9": "遵守法律义务"}, "sharing": {"title": "信息共享", "content1": "我们可能在以下情况下共享您的信息：", "item1": {"title": "服务提供商", "content": "与帮助我们提供服务的第三方服务提供商共享，如支付处理商、物流公司、客户服务提供商等。"}, "item2": {"title": "业务合作伙伴", "content": "与我们的业务合作伙伴共享，以提供您请求的服务或完成您授权的交易。"}, "item3": {"title": "法律要求", "content": "当我们真诚地相信披露是法律要求的，如响应传票、法院命令或其他法律程序。"}, "item4": {"title": "保护权利", "content": "当我们真诚地相信披露是保护我们的权利、财产或安全，或我们的用户或公众的权利、财产或安全所必需的。"}, "item5": {"title": "业务转让", "content": "在合并、收购、资产出售或类似交易的情况下，您的信息可能作为转让资产的一部分。"}, "content2": "我们不会出售您的个人信息。"}, "security": {"title": "数据安全", "content1": "我们实施适当的技术和组织措施来保护您的个人信息免受意外或非法破坏、丢失、更改、未经授权的披露或访问。", "content2": "然而，没有任何互联网传输或电子存储方法是100%安全的。因此，虽然我们努力使用商业上可接受的方式保护您的个人信息，但我们不能保证其绝对安全。"}, "retention": {"title": "数据保留", "content1": "我们将在实现本隐私政策中概述的目的所需的时间内保留您的个人信息，除非法律要求或允许更长的保留期。", "content2": "当我们不再需要使用您的个人信息时，我们将从我们的系统中删除它或对其进行匿名化处理，使其无法识别您的身份。"}, "rights": {"title": "您的权利", "content1": "根据您所在地区的适用法律，您可能拥有以下权利：", "item1": "访问您的个人信息", "item2": "更正不准确或不完整的个人信息", "item3": "删除您的个人信息", "item4": "限制或反对处理您的个人信息", "item5": "数据可携带性", "item6": "撤回同意", "content2": "要行使这些权利，请通过下面提供的联系方式与我们联系。"}, "children": {"title": "儿童隐私", "content1": "我们的服务不面向13岁以下的儿童。我们不会故意收集13岁以下儿童的个人信息。如果您是父母或监护人，并且您认为您的孩子向我们提供了个人信息，请联系我们，我们将采取措施删除这些信息。"}, "international": {"title": "国际数据传输", "content1": "您的个人信息可能会被传输到、存储在您所在国家/地区以外的国家/地区，这些国家/地区的数据保护法律可能与您所在国家/地区的法律不同。", "content2": "当我们传输您的个人信息到其他国家/地区时，我们将采取适当的措施确保您的个人信息得到足够的保护，并确保传输符合适用的数据保护法律。"}, "cookies": {"title": "<PERSON><PERSON>政策", "content1": "Cookie是放置在您设备上的小型文本文件，用于记录有关您访问我们网站的信息。我们使用Cookie和类似技术来：", "item1": "使我们的网站正常运行", "item2": "记住您的偏好设置", "item3": "了解您如何使用我们的网站", "item4": "个性化您的体验", "item5": "提供相关广告", "content2": "您可以通过浏览器设置控制和删除Cookie。然而，如果您选择禁用Cookie，您可能无法使用我们网站的某些功能。", "content3": "有关我们如何使用Cookie的更多信息，请参阅我们的Cookie政策。"}, "changes": {"title": "隐私政策变更", "content1": "我们可能会不时更新本隐私政策。当我们进行重大更改时，我们将在我们的网站上发布更新后的政策，并在适当的情况下通知您。", "content2": "我们鼓励您定期查看本隐私政策，以了解我们如何保护您的信息。"}, "contact": {"title": "联系我们", "content1": "如果您对本隐私政策有任何疑问、意见或请求，请通过以下方式联系我们：", "content2": "我们将在合理的时间内回复您的请求。"}}, "__section_cookie_policy": "==================== Cookie政策 ====================", "cookiePolicy": {"introduction": {"title": "什么是Cookie？", "content1": "Cookie是放置在您的计算机或移动设备上的小型文本文件，当您访问网站时，它允许网站\"记住\"您的操作和偏好设置。Cookie通常包含发出Cookie的网站名称、Cookie的\"寿命\"(即它在您的设备上保留多长时间)以及一个值，通常是一个随机生成的唯一数字。", "content2": "除了Cookie，我们还可能使用网络信标、像素标签和其他类似技术。这些是嵌入在网页或电子邮件中的小型图形文件，用于收集有关您如何使用我们服务的信息。在本政策中，我们将这些技术统称为\"Cookie\"。"}, "types": {"title": "我们使用的Cookie类型", "content": "我们使用以下类型的Cookie：", "essential": {"title": "必要<PERSON><PERSON>", "content": "这些Cookie对于提供您通过我们网站请求的服务是必不可少的，使您能够使用某些功能，如访问安全区域。没有这些Cookie，我们无法提供您请求的某些服务。"}, "performance": {"title": "性能和分析Cookie", "content": "这些Cookie收集有关您如何使用我们网站的信息，例如您访问的页面和您遇到的任何错误。这些Cookie不收集可识别您身份的信息，所有信息都是匿名的。我们使用这些信息来改进我们的网站，测量我们广告和网页内容的效果，并开发新功能。"}, "functional": {"title": "功能性<PERSON><PERSON>", "content": "这些Cookie允许网站记住您所做的选择（如您的用户名、语言或您所在的地区），并提供增强的、更个性化的功能。这些Cookie还可以用来记住您对文本大小、字体和网页其他可定制部分的更改。它们也可能用于提供您请求的服务，如观看视频或在社交媒体上评论。"}, "targeting": {"title": "定向和广告Cookie", "content": "这些Cookie用于投放与您和您的兴趣更相关的广告。它们还用于限制您看到广告的次数，以及帮助衡量广告活动的效果。它们通常由广告网络在网站运营商的许可下放置。它们记住您访问过的网站，这些信息可能与其他组织（如广告商）共享。"}, "thirdParty": {"title": "第三方<PERSON>", "content": "我们的网站上有一些来自第三方的Cookie，如分析合作伙伴或广告网络。这些第三方可能会收集有关您在线活动的信息，以帮助他们提供相关广告或分析我们网站的使用情况。"}}, "purposes": {"title": "Cookie的具体用途", "content": "我们使用Cookie的具体目的包括：", "item1": "验证用户身份并确保账户安全", "item2": "记住用户的登录状态，这样用户就不必在每次访问网站时都重新登录", "item3": "记住用户的语言和其他偏好设置", "item4": "分析用户如何使用我们的网站，以便我们可以改进其结构和内容", "item5": "收集统计数据，如访问者数量和使用情况", "item6": "根据用户的兴趣和行为定制内容和广告", "item7": "防止欺诈活动并改进网站安全性"}, "control": {"title": "如何控制<PERSON>ie", "content1": "大多数网络浏览器自动接受Cookie，但如果您愿意，您通常可以修改浏览器设置以拒绝Cookie。以下是关于如何在不同浏览器中管理Cookie的信息：", "content2": "请注意，如果您选择拒绝Cookie，您可能无法使用我们网站的某些功能。此外，您可以通过访问以下网站了解更多关于如何管理和删除Cookie的信息："}, "doNotTrack": {"title": "请勿追踪信号", "content": "一些浏览器包含\"请勿追踪\"(DNT)功能，可以向网站发送信号，表明您不希望您的在线活动被追踪。由于尚无统一的技术标准来识别和实施DNT信号，我们目前不会响应浏览器DNT请求。"}, "updates": {"title": "本政策的更新", "content": "我们可能会不时更新本Cookie政策，以反映我们使用Cookie的变化或出于其他运营、法律或监管原因。请定期查看本政策，以了解我们如何使用Cookie。"}, "contact": {"title": "联系我们", "content": "如果您对我们使用Cookie有任何疑问，请通过以下方式联系我们："}, "consent": {"title": "<PERSON><PERSON>同意", "content1": "当您首次访问我们的网站时，我们会通过Cookie横幅通知您我们使用Cookie。通过继续使用我们的网站，您同意我们按照本政策使用Cookie。", "content2": "如果您不希望接受Cookie，请按照上述\"如何控制Cookie\"部分中的说明调整您的浏览器设置。"}}, "__section_nav_menu": "==================== 导航菜单 ====================", "nav": {"home": "首页", "products": "商品列表", "categories": "商品分类", "search": "搜索结果", "cart": "购物车", "checkout": "结算", "account": "我的账户"}, "__section_transfer": "==================== 转运相关 ====================", "transfer": {"title": "国际转运服务", "subtitle": "快速、安全、便捷的国际物流解决方案", "description": "我们提供专业的国际转运服务，帮助您轻松将中国境内的商品运送到世界各地。", "steps": {"title": "转运流程", "step1": {"title": "创建转运单", "description": "登录账户，在个人中心创建转运单，填写包裹信息"}, "step2": {"title": "包裹寄送", "description": "将您的包裹寄送至我们的中国仓库地址"}, "step3": {"title": "入库验收", "description": "我们收到包裹后会进行验收并更新状态"}, "step4": {"title": "支付运费", "description": "确认包裹信息并支付国际运费"}, "step5": {"title": "国际配送", "description": "我们将安排包裹的国际运输"}, "step6": {"title": "签收包裹", "description": "包裹送达目的地，您签收包裹"}}, "features": {"title": "服务特色", "feature1": {"title": "全球配送", "description": "覆盖全球200多个国家和地区"}, "feature2": {"title": "专业包装", "description": "专业打包，确保物品安全运输"}, "feature3": {"title": "实时追踪", "description": "全程物流状态实时更新"}, "feature4": {"title": "多种运输方式", "description": "提供多种运输方式满足不同需求"}, "feature5": {"title": "仓储服务", "description": "提供免费仓储服务，可合箱发货"}, "feature6": {"title": "专业客服", "description": "多语言客服团队提供专业支持"}}, "faq": {"title": "常见问题", "question1": "如何创建转运单？", "answer1": "登录您的账户，进入个人中心，点击'我的转运单'，然后点击'创建转运单'按钮，填写相关信息即可。", "question2": "转运包裹的地址是什么？", "answer2": "创建转运单后，系统会自动生成一个专属于您的中国仓库地址，您需要将此地址提供给卖家或物流公司。", "question3": "支持哪些运输方式？", "answer3": "我们提供多种国际物流方式，包括普通空运、快速空运、海运等，您可以根据自己的需求选择合适的运输方式。", "question4": "如何计算运费？", "answer4": "运费根据包裹的重量、体积和目的地国家计算，您可以在我们的运费计算器中估算运费。", "question5": "包裹可以存放多久？", "answer5": "我们提供30天的免费仓储服务，超过30天将收取仓储费用。", "question6": "是否可以合箱发货？", "answer6": "是的，您可以等待多个包裹到达仓库后，选择合箱发货，这样可以节省运费。"}, "cta": {"title": "开始使用我们的转运服务", "subtitle": "只需简单几步，即可享受专业的国际转运服务", "button": "创建转运单", "loginFirst": "登录创建转运单"}, "page": {"title": "中国仓库地址", "name": "收件人：{name}", "address": "地址：{address}", "phone": "电话：{phone}", "note": "备注：请在包裹上注明您的转运单号"}, "common": {"myTransfer": "我的转运单", "createTransfer": "创建转运单", "editTransfer": "编辑转运单", "transferDetail": "转运单详情", "backToList": "返回列表", "backToDetail": "返回详情", "loading": "加载中...", "noData": "未找到转运单信息", "noDataOrCannotEdit": "未找到转运单信息或该转运单不可编辑", "returnToList": "返回转运单列表", "transferNumber": "转运单号", "createTime": "创建时间", "expressCompany": "快递公司", "expressNumber": "快递单号", "itemCount": "物品数量", "status": "状态", "actions": "操作", "viewDetail": "查看详情", "edit": "修改", "cancel": "取消", "delete": "删除", "save": "保存修改", "copySuccess": "已复制到剪贴板", "copyFailed": "复制失败", "piece": "件", "unknown": "未知", "kg": "kg", "noRemark": "无", "copyNumber": "复制单号"}, "list": {"warehouseAddress": "我的仓库地址", "searchPlaceholder": "请输入转运单号", "noTransferRecords": "暂无转运单记录", "itemList": "物品清单", "category": "类别", "name": "名称", "quantity": "数量", "weight": "重量", "totalRecords": "共 {total} 条记录，第 {current} / {totalPages} 页"}, "status": {"pending": "待入库", "warehoused": "已入库", "toBeShipped": "待发货", "shipped": "已发货", "completed": "已完成", "cancelled": "已取消", "unknown": "未知状态", "all": "全部", "inWarehouse": "已入库", "readyToShip": "待发货"}, "create": {"selectWarehouse": "选择将要寄往的仓库", "warehouseNote": "仓库仅支持不超过本人大陆地址以及境外（含港澳台）现住人地址的包裹", "selectWarehouseLabel": "选择仓库", "warehouseRequired": "请选择仓库", "expressInfo": "填写国内快递信息", "expressNote": "快递单号是您寄往我们仓库的快递单号，请确保信息准确无误", "expressCompanyLabel": "快递公司", "expressCompanyRequired": "请选择快递公司", "expressNumberLabel": "快递单号", "expressNumberRequired": "请输入快递单号", "otherExpressLabel": "请填写具体快递公司名称", "otherExpressRequired": "请填写具体快递公司名称", "productInfo": "填写需转运的商品信息", "productNote": "由于海关要求，请如实填写商品信息，包括分类、名称、数量、重量等", "product": "商品", "deleteProduct": "删除此商品", "categoryLabel": "商品分类", "categoryRequired": "请选择商品分类", "productNameLabel": "商品名称", "productNameRequired": "请输入商品名称", "quantityLabel": "数量", "quantityRequired": "请输入数量", "quantityPositive": "数量必须大于0", "weightLabel": "重量(kg)", "weightPositive": "重量必须大于0", "priceLabel": "价值(元)", "addProduct": "添加商品", "remarkInfo": "备注信息", "remarkNote": "如有特殊要求或需要说明的事项，请在此填写", "remarkPlaceholder": "请填写转运单备注信息（选填）", "remarkMaxLength": "备注不能超过300字", "notice": "注意事项", "noticeItems": ["请确保您填写的信息准确无误，特别是快递单号和商品信息。", "转运服务仅支持合法物品，禁止转运违禁品和侵权产品。", "包裹入库后，我们会通知您并提供实际重量和体积信息。", "请您在收到包裹入库通知后及时提交发货申请，如一个月内未提交发货申请，平台将收取仓储费用。"], "agreement": "我已阅读并同意《免责声明》和《转运规则及服务条款》", "agreementRequired": "请阅读并同意条款", "submit": "提交转运单", "otherExpressNote": "请填写具体快递公司名称", "createSuccess": "转运单创建成功", "createFailed": "创建转运单失败"}, "edit": {"expressInfoUnchangeable": "快递信息（不可修改）", "expressInfoNote": "快递信息是转运单的核心信息，提交后不可修改", "warehouseInfoUnchangeable": "仓库信息（不可修改）", "warehouseInfoNote": "目标仓库信息提交后不可修改", "targetWarehouse": "目标仓库", "productInfoChangeable": "商品信息（可修改）", "productInfoNote": "您可以修改商品信息，包括分类、名称、数量、重量等", "remarkChangeable": "备注信息（可修改）", "editNotice": "修改说明", "editNoticeItems": ["您只能修改商品信息和备注信息，快递信息和仓库信息不可修改。", "转运单一旦入库（状态变更为\"已入库\"），将不再允许修改任何信息。", "请确保修改后的信息准确无误，特别是商品信息。", "如需修改快递信息，请取消当前转运单并重新创建。"], "updateSuccess": "转运单修改成功", "updateFailed": "修改转运单失败"}, "detail": {"basicInfo": "基本信息", "targetWarehouse": "目标仓库", "remarkInfo": "备注信息", "itemList": "物品清单", "expressInfo": "快递信息", "expressRemark": "快递备注", "trackExpress": "查询物流", "applyForShipment": "申请发货", "applyConfirmTitle": "申请发货", "applyConfirmMessage": "确定要申请发货吗？", "applySuccess": "申请发货成功，请等待处理", "deleteConfirmTitle": "确认删除", "deleteConfirmMessage": "确定要删除这个转运单吗？此操作不可恢复！", "deleteSuccess": "转运单已删除", "deleteFailed": "删除转运单失败", "cancelConfirmTitle": "确认取消", "cancelConfirmMessage": "确定要取消这个转运单吗？", "cancelSuccess": "转运单已取消", "cancelFailed": "取消转运单失败", "onlyPendingCanEdit": "只有待入库状态的转运单可以编辑", "onlyCancelledCanDelete": "只有已取消状态的转运单可以删除", "itemDetails": "点击查看详情", "print": "打印转运单", "pieces": "件", "expressNumberNotExist": "快递单号不存在", "confirmApplyForShipment": "确定要申请发货吗？", "applyForShipmentSuccess": "申请发货成功", "confirmDelete": "确认删除", "confirmDeleteMessage": "确定要删除这个转运单吗？此操作不可恢复！"}, "warehouse": {"warehouseAddress": "我的仓库地址", "addressInfo": "仓库地址信息", "selectAddress": "请选择以下地址作为您的快递收件地址：", "receiver": "收件人", "phone": "联系电话", "address": "详细地址", "copyAddress": "复制地址", "addressNote": "请确保您的快递包裹上的收件地址与上述地址完全一致，否则可能导致包裹无法正确入库。", "myWarehouseAddress": "我的仓库地址"}, "errors": {"getListFailed": "获取转运单列表失败", "getListError": "获取转运单列表出错", "cancelError": "取消转运单出错", "deleteError": "删除转运单出错", "createError": "创建转运单出错", "updateError": "修改转运单出错", "getDetailError": "获取数据出错"}}, "__section_warehouse": "==================== 仓库管理 ====================", "warehouse": {"myWarehouse": "我的仓库", "searchPlaceholder": "请输入商品名称", "loading": "数据加载中...", "noStockItems": "暂无库存商品", "quantity": "数量", "weight": "重量", "volume": "体积", "expiry": "到期", "totalItemWeight": "物品总重量", "estimatedPackagingWeight": "预估包装材料重量", "estimatedParcelWeight": "估计包裹总重量", "selectAll": "全选", "balance": "余额", "submitParcel": "提交包裹", "weightInfo": "重量信息", "totalRecords": "共 {total} 条记录，第 {current} / {pages} 页", "productName": "商品名称", "productImage": "商品图片", "image": "图片", "productDetails": "商品详情", "status": "状态", "inboundDate": "入库日期", "expiryDate": "到期日期", "actions": "操作", "viewDetails": "查看详情", "title": "我的仓库", "search": "搜索", "createParcel": "创建包裹", "selected": "已选择", "items": "件商品", "noItems": "暂无库存商品", "pagination": "共 {total} 条记录，第 {current} / {totalPages} 页", "detail": "商品详情", "count": "数量", "inTime": "入库时间", "errors": {"fetchFailed": "获取库存列表失败", "fetchError": "获取库存列表出错"}}, "__section_warehouse_detail": "==================== 仓库详情页面 ====================", "warehouseDetail": {"title": "库存详情", "backToList": "返回列表", "loading": "加载中...", "notFound": "未找到商品信息", "sections": {"basicInfo": "基本信息", "detailedInfo": "详细信息", "properties": "商品属性", "productImage": "商品图片", "inspectionImages": "质检图片", "inspectionVideos": "质检视频", "relatedFiles": "相关文件"}, "fields": {"id": "编号", "spuName": "商品名称", "count": "数量", "weight": "重量", "volume": "体积", "status": "状态", "inTime": "入库时间", "expiredTime": "到期时间", "createTime": "创建时间"}, "status": {"normal": "正常", "locked": "锁定", "expired": "已过期", "unknown": "未知状态"}, "placeholders": {"none": "无", "file": "文件"}, "buttons": {"createParcel": "创建包裹", "download": "下载"}, "imageViewer": {"title": "图片查看"}, "videoPlayer": {"title": "视频播放", "clickToPlay": "点击播放视频"}, "errors": {"fetchFailed": "获取库存详情失败", "fetchError": "获取库存详情出错"}}, "__section_payment": "==================== 支付页面 ====================", "payment": {"title": "收银台", "orderStatus": {"submitted": "订单已提交成功，请尽快支付", "paid": "该订单已支付", "expired": "订单已过期", "notFound": "未查询到支付单信息", "remainingTime": "剩余支付时间 {h}:{m}:{s}"}, "amount": "金额:", "payButton": "立即支付", "insufficientBalance": "余额不足，请选择其他支付方式或充值后再试", "result": {"title": "支付结果", "success": "支付成功", "failed": "支付失败", "closed": "该订单已关闭", "waiting": "检测支付结果...", "unknown": "未知状态", "backToHome": "返回首页", "viewOrder": "查看订单", "repay": "重新支付"}, "paymentDialog": {"processing": "支付进行中", "usingChannel": "您正在使用{channel}支付", "unknownChannel": "未知支付方式", "message": "请在新打开的窗口中完成支付，支付完成后请点击下方按钮", "completed": "支付完成", "reselect": "重新选择支付方式", "abandon": "放弃支付", "problem": "支付遇到问题？", "helpTitle": "支付帮助", "defaultHelpMessage": "如果您在支付过程中遇到问题，请联系客服或发送邮件至 <EMAIL>", "simpleCompleted": "已完成", "simpleReselect": "重选", "simpleAbandon": "取消"}, "navigation": {"backToHome": "返回首页", "viewOrder": "查看订单"}, "notification": {"selectPayment": "请选择支付方式", "paymentFailed": "支付请求失败", "paymentError": "支付请求异常", "popupBlocked": "无法打开支付页面，请检查浏览器弹窗设置"}, "walletPay": {"title": "余额支付", "confirmMessage": "您将使用账户余额支付", "currentBalance": "当前账户余额", "confirm": "确认支付", "payPassword": "支付密码", "payPasswordPlaceholder": "请输入6位支付密码", "payPasswordRequired": "请输入支付密码", "payPasswordInvalid": "支付密码必须为6位数字"}, "selector": {"title": "选择支付方式", "fee": "手续费:", "feeTooltip": "包含费率和固定费用", "orderAmount": "订单金额：", "actualPayment": "实际支付：", "feeDescription": "手续费为", "feeDescriptionApprox": "约", "paymentMethods": {"paypal": {"title": "<PERSON><PERSON>", "description": "接受各种外币，支持信用卡和借记卡支付"}, "stripe": {"title": "Stripe", "description": "接受各种外币，支持信用卡和借记卡支付"}, "wechat": {"title": "微信支付", "description": "使用微信扫码支付"}, "alipay": {"title": "支付宝支付", "description": "使用支付宝扫码支付"}, "wallet": {"title": "余额支付", "description": "使用账户余额支付，当前余额:"}}}}, "__section_parcel": "==================== 包裹页面 ====================", "parcel": {"title": "我的包裹", "detail": {"title": "包裹详情", "backToList": "返回包裹列表", "loading": "加载包裹信息中...", "notFound": "未找到包裹信息", "sections": {"status": "包裹状态", "parcelNumber": "包裹编号", "createTime": "创建时间", "trackingNumber": "物流单号", "shippingMethod": "物流方式", "tracking": "物流跟踪", "items": "包裹商品", "address": "收货地址", "customs": "报关信息", "logistics": "物流信息", "services": "服务信息", "costs": "费用信息", "remarks": "包裹备注"}, "address": {"recipient": "收件人", "phone": "联系电话", "address": "收货地址"}, "customs": {"declaredValue": "申报价值", "clearanceCode": "清关代码", "description": "申报内容", "none": "无"}, "logistics": {"estimatedDelivery": "预计送达", "weight": "包裹重量", "noInfo": "暂无信息", "notSelected": "未选择"}, "services": {"insurance": "保险服务", "valueAdded": "增值服务", "free": "免费服务", "notPurchased": "未购买", "notSelected": "未选择"}, "costs": {"baseShipping": "基础运费", "insurance": "保险费用", "extraServices": "增值服务", "couponDiscount": "优惠券抵扣", "pointsDiscount": "积分抵扣", "total": "总计"}, "itemInfo": {"weight": "重量", "quantity": "数量"}, "copy": {"success": "已复制到剪贴板", "failed": "复制失败"}}, "tabs": {"unpaid": "待付款", "unshipped": "待发货", "shipping": "运输中", "completed": "已完成", "all": "全部"}, "search": {"placeholder": "请输入包裹号"}, "table": {"details": "包裹详情", "weight": "重量", "quantity": "数量", "amount": "金额", "status": "包裹状态", "actions": "操作"}, "parcelInfo": {"number": "包裹编号", "totalAmount": "总费用", "weightUnit": "g", "itemCount": "共{count}件商品 合计"}, "status": {"unpaid": "待付款", "unshipped": "待发货", "shipping": "运输中", "completed": "已完成", "cancelled": "已取消", "unknown": "未知状态"}, "actions": {"details": "包裹详情", "cancel": "取消包裹", "pay": "立即支付", "track": "查看物流"}, "dialog": {"cancel": {"title": "取消包裹", "message": "确定要取消该包裹吗？", "success": "包裹已取消"}, "delete": {"title": "删除包裹", "message": "确定要删除该包裹吗？删除后将无法恢复。", "success": "包裹已删除"}, "track": {"title": "物流信息", "trackingNumber": "物流单号", "shippingMethod": "物流方式", "weight": "包裹重量", "note": "物流详情请前往物流公司官网查询"}}, "errors": {"emptyId": "包裹ID不能为空", "fetchFailed": "获取包裹列表失败", "noTracking": "暂无物流信息", "cancelFailed": "取消包裹失败", "cancelError": "取消包裹出错", "deleteFailed": "删除包裹失败", "deleteError": "删除包裹出错"}, "pagination": "共 {total} 条记录，第 {current} / {pages} 页", "noRecords": "暂无包裹记录"}, "__section_wishlist": "==================== 收藏页面 ====================", "wishlist": {"title": "我的收藏", "loading": "加载中...", "noProducts": "暂无收藏商品", "browseProducts": "去浏览商品", "viewDetails": "查看详情", "hot": "热门", "confirmRemove": {"title": "确定要移除该商品吗？", "cancel": "取消", "confirm": "确定"}, "removeSuccess": "商品已从收藏中移除", "pagination": "共 {total} 条记录，第 {current}/{pages} 页"}, "__section_message": "==================== 消息中心 ====================", "messageCenter": {"inbox": {"pageTitle": "站内信", "messageType": "消息类型", "readStatus": "阅读状态", "searchPlaceholder": "搜索消息", "markAllRead": "全部已读", "batchDelete": "批量删除", "noMessages": "暂无消息", "totalRecords": "共 {total} 条记录，第 {current}/{pages} 页", "typeColumn": "类型", "titleColumn": "标题", "contentColumn": "内容", "timeColumn": "时间", "actionsColumn": "操作", "confirmDelete": "确认删除", "confirmDeleteSingle": "确定要删除这条消息吗？", "confirmDeleteMultiple": "确定要删除选中的 {count} 条消息吗？", "cancel": "取消", "delete": "删除", "deleteSuccess": "删除成功", "deleteFailed": "删除失败，请稍后重试", "markAllReadSuccess": "所有消息已标记为已读", "markAllReadFailed": "操作失败，请稍后重试", "loadFailed": "加载消息失败，请稍后重试", "read": "已读", "unread": "未读", "allMessages": "全部消息", "systemNotice": "系统通知", "orderNotice": "订单通知", "transferNotice": "转运通知", "accountNotice": "账户通知", "activityNotice": "活动通知", "viewDetails": "查看详情", "copyNumber": "复制单号"}, "consult": {"pageTitle": "我的工单", "consultType": "咨询类型", "statusFilter": "状态", "dateRange": "时间范围", "searchPlaceholder": "搜索咨询", "newConsult": "新建咨询", "noConsults": "暂无咨询", "idColumn": "编号", "typeColumn": "类型", "titleColumn": "标题", "createTimeColumn": "创建时间", "lastReplyTimeColumn": "最后回复", "statusColumn": "状态", "actionsColumn": "操作", "view": "查看", "close": "关闭", "confirmClose": "确认关闭咨询", "confirmCloseMessage": "确定要关闭此咨询吗？关闭后将无法继续回复。", "cancel": "取消", "confirm": "确认关闭", "closeSuccess": "咨询已关闭", "closeFailed": "操作失败，请稍后重试", "loadFailed": "加载咨询失败，请稍后重试", "newConsultTitle": "新建咨询", "consultTypeRequired": "咨询类型 *", "relatedOrder": "相关订单", "relatedTransfer": "相关转运单", "consultTitleRequired": "咨询标题 *", "consultContentRequired": "咨询内容 *", "attachments": "附件", "attachmentsHint": "最多3个文件，每个不超过5MB", "submit": "提交", "submitSuccess": "咨询提交成功", "submitFailed": "提交失败，请稍后重试", "fileRejected": "文件不符合要求", "filesRejected": "{count} 个文件不符合要求", "titleRule": "标题不能为空且不超过50个字符", "contentRule": "内容不能为空且不超过500个字符", "typeRule": "请选择咨询类型", "allTypes": "全部类型", "orderConsult": "订单咨询", "transferConsult": "转运咨询", "productConsult": "商品咨询", "aftersaleService": "售后服务", "complaint": "投诉建议", "otherIssue": "其他问题", "allStatuses": "全部状态", "pending": "待回复", "replied": "已回复", "closed": "已关闭"}}}