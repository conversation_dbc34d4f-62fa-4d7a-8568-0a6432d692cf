<template>
  <!-- 头部广告 -->
  <transition name="slide-up">
    <div v-if="items?.length > 0 && showTopAd" class="top">
      <div class="topAd">
        <img :src="items[0].imagepath" :alt="items[0].title || '广告'" />
        <i class="iconfont icon-guanbi closeBtn" @click="closeTopAd" />
      </div>
    </div>
  </transition>
</template>

<script setup>
import { useBannerStore } from '~/store/banner';

const bannerStore = useBannerStore();
const items = bannerStore.getBannersByTag('top');
const showTopAd = ref(true);

const closeTopAd = () => {
  showTopAd.value = false;
};
</script>

<style lang="scss" scoped>
.slide-up-enter-active,
.slide-up-leave-active {
  transition: all 0.5s ease;
}

.slide-up-enter,
.slide-up-leave-to {
  transform: translateY(0);
  opacity: 1;
}

.slide-up-leave-active {
  opacity: 0;
  transform: translateY(-100%);
}

.top {
  width: 100%;
  background-color: #4f87ff;

  .topAd {
    position: relative;
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    justify-content: center;
    align-items: center;

    img {
      width: 100%;
      height: auto;
      max-height: 100px;
      object-fit: cover;
    }

    .closeBtn {
      position: absolute;
      top: 10px;
      right: 10px;
      cursor: pointer;
      color: #fff;
      background-color: rgba(0, 0, 0, 0.3);
      border-radius: 50%;
      padding: 4px;
      font-size: 16px;
      z-index: 2;
    }
  }
}

@media (max-width: 599px) {
  .top {
    .topAd {
      img {
        max-height: 60px;
      }

      .closeBtn {
        top: 5px;
        right: 5px;
        font-size: 14px;
        padding: 3px;
      }
    }
  }
}

@media (min-width: 600px) and (max-width: 1023px) {
  .top {
    .topAd {
      img {
        max-height: 80px;
      }
    }
  }
}
</style>
